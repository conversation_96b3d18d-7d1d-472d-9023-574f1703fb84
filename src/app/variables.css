/* Layout variables converted to CSS custom properties */
:root {
    --layout-body-padding: 10px 20px;
    --layout-max-width: 1280px;
    --layout-header-height: 64px;
    --layout-header-padding: 0 50px;

    /* Ant Design color variables for compatibility */
    --text-color: rgba(0, 0, 0, 0.85);
    --error-color: #ff4d4f;
    --blue-6: #1890ff;

    /* Border radius variables - maintaining v4 consistency (2px for all components) */
    --border-radius-base: 2px;
    --border-radius-xs: 2px;
    --border-radius-sm: 2px;
    --border-radius-lg: 2px;
    --border-radius-outer: 2px;
}

/*
 * Ant Design v5 Border Radius Override
 * Maintains v4 visual consistency by setting all components to 2px border radius
 * These overrides can be superseded by component-specific styles when needed
 */

/* Button Components */
.ant-btn {
    border-radius: var(--border-radius-base);
}

.ant-btn-sm {
    border-radius: var(--border-radius-sm);
}

.ant-btn-lg {
    border-radius: var(--border-radius-lg);
}

/* Input Components */
.ant-input,
.ant-input-affix-wrapper,
.ant-input-group-addon,
.ant-input-search,
.ant-input-number,
.ant-input-password {
    border-radius: var(--border-radius-base);
}

.ant-input-sm,
.ant-input-affix-wrapper-sm,
.ant-input-number-sm {
    border-radius: var(--border-radius-sm);
}

.ant-input-lg,
.ant-input-affix-wrapper-lg,
.ant-input-number-lg {
    border-radius: var(--border-radius-lg);
}

/* Select Components */
.ant-select-selector,
.ant-select-single .ant-select-selector,
.ant-select-multiple .ant-select-selector {
    border-radius: var(--border-radius-base);
}

.ant-select-sm .ant-select-selector {
    border-radius: var(--border-radius-sm);
}

.ant-select-lg .ant-select-selector {
    border-radius: var(--border-radius-lg);
}

/* Dropdown */
.ant-dropdown,
.ant-dropdown-menu {
    border-radius: var(--border-radius-base);
}

/* Card Components */
.ant-card {
    border-radius: var(--border-radius-base);
}

.ant-card-head {
    border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

/* Modal Components */
.ant-modal-content {
    border-radius: var(--border-radius-base);
}

.ant-modal-header {
    border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

/* Drawer Components */
.ant-drawer-content {
    border-radius: var(--border-radius-base);
}

/* Table Components */
.ant-table,
.ant-table-container {
    border-radius: var(--border-radius-base);
}

.ant-table-thead > tr > th:first-child {
    border-radius: var(--border-radius-base) 0 0 0;
}

.ant-table-thead > tr > th:last-child {
    border-radius: 0 var(--border-radius-base) 0 0;
}

/* Pagination */
.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
    border-radius: var(--border-radius-base);
}

/* DatePicker Components */
.ant-picker,
.ant-picker-input,
.ant-picker-panel-container,
.ant-picker-panel {
    border-radius: var(--border-radius-base);
}

.ant-picker-sm {
    border-radius: var(--border-radius-sm);
}

.ant-picker-lg {
    border-radius: var(--border-radius-lg);
}

/* TimePicker */
.ant-picker-time-panel {
    border-radius: var(--border-radius-base);
}

/* Checkbox and Radio */
.ant-checkbox-inner,
.ant-radio-inner {
    border-radius: var(--border-radius-xs);
}

/* Switch */
.ant-switch {
    border-radius: 100px; /* Keep rounded for switches */
}

/* Progress */
.ant-progress-inner,
.ant-progress-bg {
    border-radius: var(--border-radius-base);
}

/* Alert */
.ant-alert {
    border-radius: var(--border-radius-base);
}

/* Message */
.ant-message-notice-content {
    border-radius: var(--border-radius-base);
}

/* Notification */
.ant-notification-notice {
    border-radius: var(--border-radius-base);
}

/* Tooltip */
.ant-tooltip-inner {
    border-radius: var(--border-radius-base);
}

/* Popover */
.ant-popover-inner,
.ant-popover-content {
    border-radius: var(--border-radius-base);
}

/* Badge */
.ant-badge-count,
.ant-badge-dot {
    border-radius: var(--border-radius-base);
}

/* Tag */
.ant-tag {
    border-radius: var(--border-radius-base);
}

/* Avatar */
.ant-avatar-square {
    border-radius: var(--border-radius-base);
}

/* Tabs */
.ant-tabs-tab {
    border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.ant-tabs-content-holder {
    border-radius: 0 0 var(--border-radius-base) var(--border-radius-base);
}

/* Collapse */
.ant-collapse,
.ant-collapse-item {
    border-radius: var(--border-radius-base);
}

/* Steps */
.ant-steps-item-icon {
    border-radius: var(--border-radius-base);
}

/* Upload */
.ant-upload,
.ant-upload-select,
.ant-upload-list-item {
    border-radius: var(--border-radius-base);
}

/* Rate */
.ant-rate-star {
    border-radius: var(--border-radius-xs);
}

/* Slider */
.ant-slider-handle {
    border-radius: 50%; /* Keep circular for handles */
}

.ant-slider-track,
.ant-slider-rail {
    border-radius: var(--border-radius-base);
}

/* Tree */
.ant-tree-node-content-wrapper {
    border-radius: var(--border-radius-base);
}

/* Transfer */
.ant-transfer-list,
.ant-transfer-list-header,
.ant-transfer-list-body {
    border-radius: var(--border-radius-base);
}

/* Calendar */
.ant-picker-calendar {
    border-radius: var(--border-radius-base);
}

/* Cascader */
.ant-cascader-picker,
.ant-cascader-menu {
    border-radius: var(--border-radius-base);
}

/* Mentions */
.ant-mentions {
    border-radius: var(--border-radius-base);
}

/* AutoComplete */
.ant-select-auto-complete .ant-select-selector {
    border-radius: var(--border-radius-base);
}

/* BackTop */
.ant-back-top {
    border-radius: var(--border-radius-base);
}

/* Anchor */
.ant-anchor-wrapper {
    border-radius: var(--border-radius-base);
}

/* Affix */
.ant-affix {
    border-radius: var(--border-radius-base);
}

/* Breadcrumb */
.ant-breadcrumb a {
    border-radius: var(--border-radius-xs);
}

/* Menu */
.ant-menu,
.ant-menu-item,
.ant-menu-submenu-title {
    border-radius: var(--border-radius-base);
}

/* Segmented */
.ant-segmented,
.ant-segmented-item {
    border-radius: var(--border-radius-base);
}

/* Image */
.ant-image,
.ant-image-img {
    border-radius: var(--border-radius-base);
}

/* Statistic */
.ant-statistic {
    border-radius: var(--border-radius-base);
}

/* QR Code */
.ant-qrcode {
    border-radius: var(--border-radius-base);
}

/* Watermark */
.ant-watermark {
    border-radius: var(--border-radius-base);
}

/* Tour */
.ant-tour,
.ant-tour-content {
    border-radius: var(--border-radius-base);
}

/* FloatButton */
.ant-float-btn {
    border-radius: 50%; /* Keep circular for float buttons */
}

/* App */
.ant-app {
    border-radius: var(--border-radius-base);
}

/* Flex */
.ant-flex {
    border-radius: var(--border-radius-base);
}

/* Typography */
.ant-typography-copy,
.ant-typography-edit {
    border-radius: var(--border-radius-xs);
}
