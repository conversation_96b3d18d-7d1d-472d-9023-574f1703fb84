/* Layout variables converted to CSS custom properties */
:root {
    --layout-body-padding: 10px 20px;
    --layout-max-width: 1280px;
    --layout-header-height: 64px;
    --layout-header-padding: 0 50px;

    /* Ant Design color variables for compatibility */
    --text-color: rgba(0, 0, 0, 0.85);
    --error-color: #ff4d4f;
    --blue-6: #1890ff;

    /* Border radius variables - maintaining v4 consistency */
    --border-radius-base: 2px;
    --border-radius-sm: 2px;
    --border-radius-lg: 2px;
    --border-radius-xl: 2px;
}

/*
 * Ant Design v5 Border Radius Override
 * Reverting all components to v4's uniform 2px border radius
 * This ensures zero visual regression from v4 to v5 migration
 */

/* Button Components */
.ant-btn,
.ant-btn-primary,
.ant-btn-default,
.ant-btn-dashed,
.ant-btn-text,
.ant-btn-link,
.ant-btn-ghost,
.ant-btn-dangerous,
.ant-btn-sm,
.ant-btn-lg {
    border-radius: var(--border-radius-base) !important;
}

/* Input Components */
.ant-input,
.ant-input-sm,
.ant-input-lg,
.ant-input-group,
.ant-input-group-addon,
.ant-input-group-wrapper,
.ant-input-search,
.ant-input-search-button,
.ant-input-number,
.ant-input-number-sm,
.ant-input-number-lg,
.ant-input-password,
.ant-textarea {
    border-radius: var(--border-radius-base) !important;
}

/* Select Components */
.ant-select,
.ant-select-selector,
.ant-select-sm .ant-select-selector,
.ant-select-lg .ant-select-selector,
.ant-select-dropdown,
.ant-select-item,
.ant-select-item-option,
.ant-cascader,
.ant-cascader-picker,
.ant-tree-select,
.ant-tree-select-selector {
    border-radius: var(--border-radius-base) !important;
}

/* Date Picker Components */
.ant-picker,
.ant-picker-sm,
.ant-picker-lg,
.ant-picker-dropdown,
.ant-picker-panel,
.ant-picker-cell,
.ant-picker-cell-inner,
.ant-picker-range,
.ant-picker-range-separator {
    border-radius: var(--border-radius-base) !important;
}

/* Card Components */
.ant-card,
.ant-card-head,
.ant-card-body,
.ant-card-small,
.ant-card-bordered,
.ant-card-hoverable {
    border-radius: var(--border-radius-base) !important;
}

/* Modal Components */
.ant-modal,
.ant-modal-content,
.ant-modal-header,
.ant-modal-body,
.ant-modal-footer,
.ant-drawer,
.ant-drawer-content,
.ant-drawer-header,
.ant-drawer-body {
    border-radius: var(--border-radius-base) !important;
}

/* Form Components */
.ant-form-item,
.ant-form-item-control,
.ant-form-item-control-input,
.ant-checkbox,
.ant-checkbox-inner,
.ant-radio,
.ant-radio-inner,
.ant-switch,
.ant-slider-handle,
.ant-upload,
.ant-upload-select,
.ant-upload-list-item {
    border-radius: var(--border-radius-base) !important;
}

/* Table Components */
.ant-table,
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td,
.ant-table-cell,
.ant-table-filter-dropdown,
.ant-pagination,
.ant-pagination-item,
.ant-pagination-item-link {
    border-radius: var(--border-radius-base) !important;
}

/* Navigation Components */
.ant-menu,
.ant-menu-item,
.ant-menu-submenu,
.ant-menu-submenu-title,
.ant-dropdown,
.ant-dropdown-menu,
.ant-dropdown-menu-item,
.ant-tabs,
.ant-tabs-tab,
.ant-tabs-content,
.ant-breadcrumb,
.ant-steps-item {
    border-radius: var(--border-radius-base) !important;
}

/* Feedback Components */
.ant-alert,
.ant-message,
.ant-message-notice,
.ant-notification,
.ant-notification-notice,
.ant-popover,
.ant-popover-content,
.ant-popover-inner,
.ant-tooltip,
.ant-tooltip-content,
.ant-tooltip-inner,
.ant-progress,
.ant-progress-bg {
    border-radius: var(--border-radius-base) !important;
}

/* Layout Components */
.ant-layout,
.ant-layout-header,
.ant-layout-content,
.ant-layout-footer,
.ant-layout-sider,
.ant-affix,
.ant-anchor,
.ant-back-top,
.ant-divider {
    border-radius: var(--border-radius-base) !important;
}

/* Data Display Components */
.ant-avatar,
.ant-avatar-sm,
.ant-avatar-lg,
.ant-badge,
.ant-badge-count,
.ant-calendar,
.ant-carousel,
.ant-collapse,
.ant-collapse-item,
.ant-collapse-header,
.ant-collapse-content,
.ant-list,
.ant-list-item,
.ant-statistic,
.ant-tag,
.ant-timeline,
.ant-timeline-item,
.ant-tree,
.ant-tree-node-content-wrapper {
    border-radius: var(--border-radius-base) !important;
}

/* Additional Component States and Variants */
.ant-btn:hover,
.ant-btn:focus,
.ant-btn:active,
.ant-btn-primary:hover,
.ant-btn-primary:focus,
.ant-btn-primary:active,
.ant-input:hover,
.ant-input:focus,
.ant-input-focused,
.ant-select:hover .ant-select-selector,
.ant-select-focused .ant-select-selector,
.ant-picker:hover,
.ant-picker-focused {
    border-radius: var(--border-radius-base) !important;
}

/* Dropdown and Popup Components */
.ant-dropdown-menu,
.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu,
.ant-select-dropdown,
.ant-select-item,
.ant-cascader-menu,
.ant-cascader-menu-item,
.ant-auto-complete-dropdown,
.ant-mentions-dropdown,
.ant-time-picker-dropdown,
.ant-color-picker-dropdown {
    border-radius: var(--border-radius-base) !important;
}

/* Loading and Skeleton Components */
.ant-spin,
.ant-spin-container,
.ant-skeleton,
.ant-skeleton-content,
.ant-skeleton-paragraph,
.ant-skeleton-title,
.ant-skeleton-avatar,
.ant-skeleton-button,
.ant-skeleton-input {
    border-radius: var(--border-radius-base) !important;
}

/* Transfer and Tree Components */
.ant-transfer,
.ant-transfer-list,
.ant-transfer-list-header,
.ant-transfer-list-body,
.ant-transfer-list-content,
.ant-transfer-list-content-item,
.ant-tree-select-dropdown,
.ant-tree-select-tree,
.ant-tree-select-tree-node-content-wrapper {
    border-radius: var(--border-radius-base) !important;
}

/* Rate and Slider Components */
.ant-rate,
.ant-rate-star,
.ant-slider,
.ant-slider-rail,
.ant-slider-track,
.ant-slider-handle,
.ant-slider-step,
.ant-slider-dot {
    border-radius: var(--border-radius-base) !important;
}

/* Image and Upload Components */
.ant-image,
.ant-image-preview,
.ant-image-preview-img,
.ant-upload,
.ant-upload-select,
.ant-upload-list,
.ant-upload-list-item,
.ant-upload-list-item-thumbnail,
.ant-upload-list-item-info {
    border-radius: var(--border-radius-base) !important;
}

/* Segmented and Space Components */
.ant-segmented,
.ant-segmented-item,
.ant-segmented-item-selected,
.ant-space,
.ant-space-item,
.ant-float-btn,
.ant-float-btn-group,
.ant-watermark {
    border-radius: var(--border-radius-base) !important;
}

/* Tour and QR Code Components */
.ant-tour,
.ant-tour-content,
.ant-tour-inner,
.ant-qrcode,
.ant-qrcode-canvas,
.ant-result,
.ant-result-content,
.ant-empty,
.ant-empty-image {
    border-radius: var(--border-radius-base) !important;
}

/* Ensure nested components also inherit the border radius */
.ant-btn .ant-btn,
.ant-input-group .ant-input,
.ant-input-group .ant-btn,
.ant-input-search .ant-input,
.ant-input-search .ant-btn,
.ant-select-multiple .ant-select-selection-item,
.ant-tag-checkable,
.ant-tag-checkable-checked {
    border-radius: var(--border-radius-base) !important;
}

/* Override any border-radius set by CSS-in-JS */
[class*='ant-'] {
    --antd-border-radius: var(--border-radius-base) !important;
}

/* Specific overrides for components that might use different border radius values */
.ant-card-cover img,
.ant-image-img,
.ant-avatar-image,
.ant-upload-list-item-thumbnail img {
    border-radius: var(--border-radius-base) !important;
}
