import type { PickerProps } from 'antd/es/date-picker/generatePicker';
import dayjs from 'dayjs';
import { useField } from 'formik';
import React, { memo, useState, useCallback, useEffect, useMemo } from 'react';
import styled, { css } from 'styled-components';
import DayjsDatePicker from './DayjsDatePicker';
import type { FormItemProps } from './FormItem';
import FormItem from './FormItem';

export const TimePicker = styled(DayjsDatePicker)<{ $readOnly: boolean }>`
    width: 100%;

    &.ant-picker {
        background-color: transparent;
        box-shadow: none;
        border-top: none;
        border-left: none;
        border-right: none;

        & input {
            font-size: 16px;
        }

        &.ant-picker-disabled {
            ${props =>
                props.$readOnly &&
                css`
                    background-color: #fff;

                    & .ant-picker-input > input[disabled] {
                        color: var(--text-color, rgba(0, 0, 0, 0.85));
                        cursor: text;
                    }
                `}

            background-color: transparent;

            & .ant-picker-suffix {
                display: none;
            }
        }

        & .anticon {
            color: var(--ant-primary-color);
        }
    }
`;

export type TimePickerFieldProps = Omit<PickerProps<dayjs.Dayjs>, 'value' | 'onChange'> &
    Pick<FormItemProps, 'label'> & {
        name: string;
        required?: boolean;
        readOnly?: boolean;
        minuteStep?: number;
        errorIcon?: React.ReactNode;
    };

const TimePickerField = ({
    name,
    required,
    label,
    disabled,
    minuteStep = 1,
    readOnly,
    errorIcon,
    ...props
}: TimePickerFieldProps) => {
    const [field, meta, { setValue: setFieldValue, setTouched }] = useField({ name });

    const defaultValue = useMemo(() => {
        if (meta.initialValue) {
            const wrapped = dayjs(meta.initialValue);

            if (wrapped.isValid()) {
                return wrapped;
            }
        }

        return null;
    }, [meta.initialValue]);

    const [value, setValue] = useState<dayjs.Dayjs | undefined>();

    useEffect(() => {
        const date = field.value ? dayjs(field.value) : undefined;
        // set internal date value
        setValue(date);
    }, [field.value]);

    const onChange = useCallback(
        date => {
            // set formik date value
            // dates will be converted to native Date object
            let newDate;

            if (date) {
                newDate = date.toDate();
            }

            setFieldValue(newDate);
            setTouched(true);
        },
        [setFieldValue, setTouched]
    );

    const disableField = disabled || readOnly;

    return (
        <FormItem errorIcon={errorIcon} label={label} meta={meta} required={required}>
            <TimePicker
                data-cy={`form-date-picker-${name}`}
                disabled={disableField}
                picker="time"
                // spread props
                {...props}
                $readOnly={readOnly}
                defaultValue={defaultValue}
                minuteStep={minuteStep}
                onChange={onChange}
                value={value}
            />
        </FormItem>
    );
};

export default memo(TimePickerField);
