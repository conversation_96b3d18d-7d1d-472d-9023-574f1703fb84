import type { FormItemProps as AntdFormItemProps } from 'antd';
import { Form } from 'antd';
import isEqual from 'fast-deep-equal';
import type { FieldMetaProps } from 'formik';
import { isNil } from 'lodash/fp';
import React, { useMemo } from 'react';
import styled, { css } from 'styled-components';

export type FormItemProps = {
    // highlight label for error warning
    isError?: boolean;
    meta?: Pick<FieldMetaProps<any>, 'error' | 'touched' | 'initialValue' | 'value'>;
    errorIcon?: React.ReactNode;
    // make it optional so it can be use in table header
    children?: JSX.Element;
    isTableInput?: boolean;
    shouldUseDefaultHelp?: boolean;
} & Omit<AntdFormItemProps, 'validateStatus'>;

export const StyledFormItem = styled(Form.Item)<{ isError?: boolean; isTableInput?: boolean }>`
    & .ant-form-item-label {
        & > label {
            color: #000;

            ${props =>
                props.isError &&
                css`
                    color: var(--error-color, #ff4d4f);
                `}

            width: 100%;
        }
    }
    & .ant-form-item-control {
        font-size: 16px;
    }

    ${props =>
        !isNil(props.children) &&
        props.isTableInput &&
        css`
        & .ant-form-item-control-input {
            padding-top 22px;
        }
    `}

    ${props =>
        isNil(props.children) &&
        css`
            & {
                margin-bottom: ${props.isTableInput ? '0px' : '0'};
            }
            & .ant-form-item-label,
            & .ant-col-24.ant-form-item-label,
            & .ant-col-xl-24.ant-form-item-label {
                padding-bottom: 0;
            }

            & .ant-form-item-control-input {
                min-height: 0;
            }
        `}

    .ant-form-item-explain.ant-form-item-explain-connected:has(> div.ant-form-item-explain-error) {
        height: 22px;
    }
    .ant-form-item-explain.ant-form-item-explain-connected:not(:has(> div.ant-form-item-explain-error)) {
        height: 24px;
    }
`;

type WrapHelpProps = Pick<FormItemProps, 'errorIcon' | 'help'> & {
    hasError?: boolean;
    errorMessage?: React.ReactNode;
};

export const WrapHelp = ({ hasError, errorMessage, errorIcon, help }: WrapHelpProps) => {
    if (hasError && errorMessage) {
        return (
            <span className="form-item-error-container">
                {errorIcon && <span className="form-item-error-icon">{errorIcon}</span>}
                {errorMessage && <span className="form-item-error-message">{errorMessage}</span>}
            </span>
        );
    }

    if (help) {
        return <span>{help}</span>;
    }

    return null;
};

const FormItem = ({
    meta,
    children,
    isTableInput = false,
    errorIcon,
    shouldUseDefaultHelp = true,
    ...props
}: FormItemProps) => {
    const valueChanged =
        !isEqual(meta?.initialValue, meta?.value) &&
        // prevent validation error shown immediately after field added through user action
        meta?.initialValue !== undefined &&
        meta?.value !== null &&
        meta?.value !== '';
    const hasError = !!meta?.error && (meta?.touched || valueChanged);

    // Skip error message for array for now
    // TODO: better handler for array error messages
    const errorMessage = Array.isArray(meta?.error) ? '' : meta?.error;

    const hasHelp = useMemo(() => (hasError && errorMessage) || props.help, [errorMessage, hasError, props.help]);

    return (
        <StyledFormItem
            {...props}
            {...(shouldUseDefaultHelp &&
                hasHelp && {
                    help: (
                        <WrapHelp
                            errorIcon={errorIcon}
                            errorMessage={errorMessage}
                            hasError={hasError}
                            help={props.help}
                        />
                    ),
                })}
            validateStatus={hasError ? 'error' : 'success'}
        >
            {children}
        </StyledFormItem>
    );
};

export default FormItem;
