import type { PickerProps, RangePickerProps } from 'antd/es/date-picker/generatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useField } from 'formik';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import DayjsDatePicker from './DayjsDatePicker';
import type { FormItemProps } from './FormItem';
import FormItem from './FormItem';
import { classNameGenerator } from './TimePickerField';

const SharedDatePickerStyle = ($readOnly: boolean) => css`
    width: 100%;

    &.ant-picker {
        background-color: transparent;

        & input {
            font-size: var(--input-font-size, 16px);

            &::placeholder {
                font-size: var(--input-font-size, 16px);
                color: #535457;
            }
        }

        &.ant-picker-disabled {
            ${$readOnly &&
            css`
                & .ant-picker-input > input[disabled] {
                    color: var(--text-color, rgba(0, 0, 0, 0.85));
                    cursor: text;
                    border: 1px solid #d9d9d9;
                }
                background-color: #f5f5f5;
            `}

            background-color: transparent;

            & .ant-picker-suffix {
                display: none;
            }
        }

        & .anticon {
            color: var(--ant-primary-color);
        }
    }
`;

const StyledDatePickerFormItem = styled.div`
    & .ant-form-item.ant-form-item-has-error .ant-form-item-margin-offset {
        display: none;
    }

    .ant-form-item-label {
        padding: var(--datepicker-label-padding-form-item);
    }
`;

export const StyledDatePicker = styled(DayjsDatePicker)<{ $readOnly: boolean; applyDefaultStyles?: boolean }>`
    ${props => SharedDatePickerStyle(props.$readOnly)}

    ${props =>
        props.applyDefaultStyles &&
        css`
            &.ant-picker {
                box-shadow: none;
                border-top: none;
                border-left: none;
                border-right: none;
            }
        `}
`;

// Function to append the current time to a given date
// When user manually enters a date, the time is not set
// This function sets the time to the current date
const appendCurrentTime = (date: Dayjs) => {
    const currentTime = dayjs();

    return date.set('hour', currentTime.hour()).set('minute', currentTime.minute()).set('second', currentTime.second());
};

export type DatePickerFieldProps = Omit<PickerProps<dayjs.Dayjs>, 'value' | 'onChange'> &
    Pick<FormItemProps, 'label' | 'tooltip'> & {
        name: string;
        required?: boolean;
        readOnly?: boolean;
        applyDefaultStyles?: boolean;
    } & Pick<RangePickerProps<dayjs.Dayjs>, 'disabledTime'> & {
        errorIcon?: React.ReactNode;
    };

const DatePickerField = ({
    name,
    required,
    label,
    disabled,
    tooltip,
    readOnly,
    picker,
    format,
    defaultValue,
    errorIcon,
    applyDefaultStyles = true,
    placeholder,
    className,
    ...props
}: DatePickerFieldProps) => {
    const { t } = useTranslation('common');
    const [field, meta, { setValue: setFieldValue, setTouched }] = useField({ name });

    const defaultDatepickerValue = useMemo(() => {
        if (defaultValue) {
            return defaultValue;
        }
        if (meta.initialValue) {
            const wrapped = dayjs(meta.initialValue);

            if (wrapped.isValid()) {
                return wrapped;
            }
        }

        return null;
    }, [defaultValue, meta.initialValue]);

    const [value, setValue] = useState<dayjs.Dayjs | undefined>();

    useEffect(() => {
        const date = field.value ? dayjs(field.value) : undefined;
        // set internal date value
        setValue(date);
    }, [field.value]);

    const onChange = useCallback(
        (date: Dayjs) => {
            // set formik date value
            // dates will be converted to native Date object
            let newDate;

            if (date) {
                const dateWithCurrentTime = appendCurrentTime(date);
                newDate = dateWithCurrentTime.toDate();
            }
            setTouched(true);
            setFieldValue(newDate);
        },
        [setFieldValue, setTouched]
    );

    const disableField = disabled || readOnly;

    const translatedPlaceholder = useMemo(() => {
        if (placeholder) {
            return placeholder;
        }
        switch (picker) {
            case 'week':
            case 'month':
            case 'quarter':
            case 'year':
                return t(`antd:DatePicker.lang.${picker}Placeholder`);

            default:
                return t('antd:DatePicker.lang.placeholder');
        }
    }, [placeholder, picker, t]);

    const generatedClassName = useMemo(() => classNameGenerator(), []);

    return (
        <StyledDatePickerFormItem>
            <FormItem errorIcon={errorIcon} label={label} meta={meta} required={required} tooltip={tooltip}>
                <StyledDatePicker
                    className={`${generatedClassName} ${className ?? ''}`}
                    data-cy={`form-date-picker-${name}`}
                    disabled={disableField}
                    format={!format && picker === 'date' ? t('common:formats.datePicker') : format}
                    getPopupContainer={() =>
                        generatedClassName ? document.querySelector(`.${generatedClassName}`) : null
                    }
                    picker={picker || 'date'}
                    placeholder={translatedPlaceholder}
                    // spread props
                    {...props}
                    $readOnly={readOnly}
                    applyDefaultStyles={applyDefaultStyles}
                    defaultValue={defaultDatepickerValue}
                    onChange={onChange}
                    value={value}
                />
            </FormItem>
        </StyledDatePickerFormItem>
    );
};

export default memo(DatePickerField);
