import path from 'path';
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import type { Configuration, RuleSetRule } from 'webpack';
import webpack from 'webpack';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import { WebpackManifestPlugin } from 'webpack-manifest-plugin';
import WebpackBar from 'webpackbar';
import {
    webpackMode,
    srcDirname,
    rootDirname,
    isBuildIntentDevelopment,
    isBuildIntentProduction,
    buildDirname,
} from '../../variables';
import getBabelRule from './babel';
import getCache from './cache';
import optimization, { devtoolModuleFilenameTemplate, getDevTools } from './optimization';
import getStyleRule from './style';

const dayjsExtend = path.resolve(srcDirname, 'dayjs.extend.ts');

// is it running in an interactive shell
const isInteractive = process.stdout.isTTY && !!process.env.CI;

const graphqlRule: RuleSetRule = {
    test: /\.graphql$/,
    exclude: /node_modules/,
    loader: require.resolve('graphql-tag/loader'),
};

const svgRule: RuleSetRule = {
    test: /\.svg$/,
    use: [require.resolve('@svgr/webpack')],
};

const progressHandler =
    (webpackConfigName: string) =>
    (percentage: number, message: string, ...args: string[]) => {
        const verbosePercentage = percentage * 100;
        const baseMessage = `[${webpackConfigName}] ${verbosePercentage.toFixed(2)}% ${message}`;

        if (args.length) {
            console.info(`${baseMessage} (${args.join(', ')})`);
        } else {
            console.info(baseMessage);
        }
    };

const notNillOnly = <T>(value: T | null | undefined | false): value is T =>
    value !== null && value !== undefined && value !== false;

const appDevTools = getDevTools(false);

const appConfig: Configuration = {
    name: 'app',
    mode: webpackMode,

    target: `browserslist:${webpackMode}`,

    entry: {
        app: [
            isBuildIntentDevelopment && require.resolve('webpack-hot-middleware/client'),
            // TODO: delete if less migration is completed
            // require.resolve('antd/dist/antd.variable.less'),
            path.resolve(srcDirname, 'app/global.less'),
            dayjsExtend,
            path.resolve(srcDirname, 'app/index.tsx'),
        ].filter(Boolean),
    },

    resolve: {
        extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],
        mainFields: ['browser', 'module', 'main'],
        alias: {
            '@sentry/node': '@sentry/react',
        },
        fallback: {
            os: false,
            crypto: false,
            zlib: false,
            https: false,
        },
    },

    output: {
        publicPath: undefined,
        path: path.resolve(buildDirname, 'public'),
        filename: isBuildIntentDevelopment ? 'static/chunks/[name].js' : 'static/chunks/[name]-[chunkhash].js',
        chunkFilename: isBuildIntentDevelopment ? 'static/chunks/[name].js' : 'static/chunks/[name]-[chunkhash].js',
        library: '_N_E',
        libraryTarget: 'assign',
        hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',
        hotUpdateMainFilename: 'static/webpack/[fullhash].hot-update.json',
        devtoolModuleFilenameTemplate,
    },

    // do not show performance hints
    performance: false,

    bail: isBuildIntentProduction,
    devtool: appDevTools.devtools,

    module: {
        rules: [getBabelRule(false), getStyleRule(), graphqlRule, svgRule].filter(Boolean),
    },

    plugins: [
        appDevTools.plugin,

        // replace antd moment with dayjs
        new AntdDayjsWebpackPlugin(),

        new WebpackManifestPlugin({
            fileName: path.join(buildDirname, 'manifest.json'),
            writeToFileEmit: true,
            generate: (seed, files) =>
                files.reduce((manifest, file) => {
                    if (!file.isInitial) {
                        return manifest;
                    }

                    let assetType = null;

                    if (file.path.match(/\.js$/)) {
                        assetType = 'js';
                    } else if (file.path.match(/\.css$/)) {
                        assetType = 'css';
                    }

                    if (!assetType) {
                        return manifest;
                    }

                    if (!manifest[file.chunk.name]) {
                        // eslint-disable-next-line no-param-reassign
                        manifest[file.chunk.name] = { js: [], css: [] };
                    }

                    manifest[file.chunk.name][assetType].push(file.path);

                    return manifest;
                }, {}),
        }),

        new webpack.DefinePlugin({
            'process.browser': JSON.stringify(true),
            'process.isDev': JSON.stringify(isBuildIntentDevelopment),
            'process.useIstanbul': JSON.stringify(!!process.env.USE_ISTANBUL),
        }),

        // copy static assets for server to bundle * server them
        new CopyPlugin({
            patterns: [{ from: path.join(rootDirname, 'public'), to: './' }],
        }),

        // hot reload with react refresh
        isBuildIntentDevelopment && new webpack.HotModuleReplacementPlugin(),
        isBuildIntentDevelopment &&
            new ReactRefreshWebpackPlugin({
                overlay: {
                    sockIntegration: 'whm',
                },
            }),

        // we want to minimize the CSS
        new MiniCssExtractPlugin({
            filename: 'static/css/[contenthash].css',
            chunkFilename: 'static/css/[contenthash].css',
            ignoreOrder: true,
        }),

        // provide reporting when building for production
        isBuildIntentProduction &&
            new BundleAnalyzerPlugin({
                reportFilename: path.join(rootDirname, 'report.html'),
                analyzerMode: 'static',
                openAnalyzer: false,
            }),

        // in TTY improve build status
        isBuildIntentProduction && isInteractive && new WebpackBar({ name: 'app', profile: true }),
        isBuildIntentProduction && !isInteractive && new webpack.ProgressPlugin(progressHandler('app')),
    ].filter(notNillOnly),

    cache: getCache('app'),

    // custom optimization for the front-end apps
    optimization,

    infrastructureLogging: { level: isBuildIntentProduction ? 'info' : 'error' },
};

export default appConfig;
